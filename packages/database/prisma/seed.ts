import { PrismaClient, UserRole, LeadSource, LeadStatus, PropertyType, PropertyStatus } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Create admin user
  const hashedPassword = await bcrypt.hash('admin123', 10);
  
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'Admin',
      lastName: 'User',
      role: UserRole.ADMIN,
    },
  });

  // Create agent users
  const agent1 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: await bcrypt.hash('agent123', 10),
      firstName: '<PERSON>',
      lastName: '<PERSON>',
      role: UserRole.AGENT,
    },
  });

  const agent2 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: await bcrypt.hash('agent123', 10),
      firstName: 'Sarah',
      lastName: 'Johnson',
      role: UserRole.AGENT,
    },
  });

  // Create sample properties
  const property1 = await prisma.property.create({
    data: {
      title: 'Beautiful Family Home',
      description: 'A stunning 4-bedroom family home in a quiet neighborhood',
      address: '123 Main Street',
      city: 'Springfield',
      state: 'IL',
      zipCode: '62701',
      price: 350000,
      bedrooms: 4,
      bathrooms: 2.5,
      sqft: 2500,
      lotSize: 0.25,
      yearBuilt: 2010,
      type: PropertyType.HOUSE,
      status: PropertyStatus.AVAILABLE,
      features: {
        garage: true,
        pool: false,
        fireplace: true,
        basement: true
      },
      images: [
        'https://example.com/image1.jpg',
        'https://example.com/image2.jpg'
      ]
    },
  });

  const property2 = await prisma.property.create({
    data: {
      title: 'Modern Downtown Condo',
      description: 'Luxury condo with city views',
      address: '456 Oak Avenue',
      city: 'Springfield',
      state: 'IL',
      zipCode: '62702',
      price: 275000,
      bedrooms: 2,
      bathrooms: 2,
      sqft: 1200,
      yearBuilt: 2020,
      type: PropertyType.CONDO,
      status: PropertyStatus.AVAILABLE,
      features: {
        balcony: true,
        gym: true,
        concierge: true
      },
      images: [
        'https://example.com/condo1.jpg',
        'https://example.com/condo2.jpg'
      ]
    },
  });

  // Create sample leads
  const lead1 = await prisma.lead.create({
    data: {
      firstName: 'Michael',
      lastName: 'Brown',
      email: '<EMAIL>',
      phone: '******-0123',
      source: LeadSource.WEBSITE,
      status: LeadStatus.NEW,
      assignedTo: agent1.id,
      description: 'Interested in family homes under $400k',
    },
  });

  const lead2 = await prisma.lead.create({
    data: {
      firstName: 'Emily',
      lastName: 'Davis',
      email: '<EMAIL>',
      phone: '******-0124',
      source: LeadSource.REFERRAL,
      status: LeadStatus.CONTACTED,
      assignedTo: agent2.id,
      description: 'Looking for a downtown condo',
    },
  });

  // Create contacts for leads
  await prisma.contact.create({
    data: {
      leadId: lead1.id,
      firstName: 'Michael',
      lastName: 'Brown',
      email: '<EMAIL>',
      phone: '******-0123',
      address: '789 Elm Street',
      city: 'Springfield',
      state: 'IL',
      zipCode: '62703',
      preferences: {
        maxPrice: 400000,
        minBedrooms: 3,
        preferredAreas: ['Downtown', 'Suburbs']
      }
    },
  });

  await prisma.contact.create({
    data: {
      leadId: lead2.id,
      firstName: 'Emily',
      lastName: 'Davis',
      email: '<EMAIL>',
      phone: '******-0124',
      address: '321 Pine Street',
      city: 'Springfield',
      state: 'IL',
      zipCode: '62704',
      preferences: {
        maxPrice: 300000,
        preferredType: 'CONDO',
        preferredAreas: ['Downtown']
      }
    },
  });

  console.log('✅ Database seeding completed successfully!');
  console.log(`👤 Admin user: <EMAIL> / admin123`);
  console.log(`👤 Agent 1: <EMAIL> / agent123`);
  console.log(`👤 Agent 2: <EMAIL> / agent123`);
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
