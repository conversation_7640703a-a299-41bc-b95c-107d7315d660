// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  firstName String
  lastName  String
  role      UserRole @default(AGENT)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  assignedLeads Lead[]
  deals         Deal[]
  notes         Note[]
  reminders     Reminder[]

  @@map("users")
}

model Lead {
  id          String     @id @default(cuid())
  firstName   String
  lastName    String
  email       String?
  phone       String?
  source      LeadSource
  status      LeadStatus @default(NEW)
  assignedTo  String?
  description String?
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt

  // Relations
  assignedAgent User?  @relation(fields: [assignedTo], references: [id])
  contact       Contact?
  deals         Deal[]
  notes         Note[]
  reminders     <PERSON>mind<PERSON>[]

  @@map("leads")
}

model Contact {
  id          String  @id @default(cuid())
  leadId      String  @unique
  firstName   String
  lastName    String
  email       String?
  phone       String?
  address     String?
  city        String?
  state       String?
  zipCode     String?
  preferences Json?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  lead  Lead   @relation(fields: [leadId], references: [id], onDelete: Cascade)
  deals Deal[]

  @@map("contacts")
}

model Property {
  id          String        @id @default(cuid())
  title       String
  description String?
  address     String
  city        String
  state       String
  zipCode     String
  price       Decimal
  bedrooms    Int?
  bathrooms   Decimal?
  sqft        Int?
  lotSize     Decimal?
  yearBuilt   Int?
  type        PropertyType
  status      PropertyStatus @default(AVAILABLE)
  features    Json?
  images      String[]
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  // Relations
  deals Deal[]

  @@map("properties")
}

model Deal {
  id         String     @id @default(cuid())
  leadId     String
  contactId  String?
  propertyId String?
  agentId    String
  value      Decimal
  status     DealStatus @default(PROSPECTING)
  stage      String
  closeDate  DateTime?
  notes      String?
  createdAt  DateTime   @default(now())
  updatedAt  DateTime   @updatedAt

  // Relations
  lead     Lead      @relation(fields: [leadId], references: [id])
  contact  Contact?  @relation(fields: [contactId], references: [id])
  property Property? @relation(fields: [propertyId], references: [id])
  agent    User      @relation(fields: [agentId], references: [id])
  notes    Note[]
  reminders Reminder[]

  @@map("deals")
}

model Note {
  id        String   @id @default(cuid())
  content   String
  leadId    String?
  dealId    String?
  authorId  String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  lead   Lead? @relation(fields: [leadId], references: [id])
  deal   Deal? @relation(fields: [dealId], references: [id])
  author User  @relation(fields: [authorId], references: [id])

  @@map("notes")
}

model Reminder {
  id          String        @id @default(cuid())
  title       String
  description String?
  dueDate     DateTime
  isCompleted Boolean       @default(false)
  type        ReminderType
  leadId      String?
  dealId      String?
  userId      String
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  // Relations
  lead Lead? @relation(fields: [leadId], references: [id])
  deal Deal? @relation(fields: [dealId], references: [id])
  user User  @relation(fields: [userId], references: [id])

  @@map("reminders")
}

// Enums
enum UserRole {
  ADMIN
  AGENT
}

enum LeadSource {
  WEBSITE
  REFERRAL
  SOCIAL_MEDIA
  COLD_CALL
  EMAIL_CAMPAIGN
  WALK_IN
  OTHER
}

enum LeadStatus {
  NEW
  CONTACTED
  QUALIFIED
  PROPOSAL_SENT
  NEGOTIATING
  CLOSED_WON
  CLOSED_LOST
}

enum PropertyType {
  HOUSE
  APARTMENT
  CONDO
  TOWNHOUSE
  LAND
  COMMERCIAL
  OTHER
}

enum PropertyStatus {
  AVAILABLE
  UNDER_CONTRACT
  SOLD
  OFF_MARKET
}

enum DealStatus {
  PROSPECTING
  QUALIFICATION
  PROPOSAL
  NEGOTIATION
  CLOSING
  CLOSED_WON
  CLOSED_LOST
}

enum ReminderType {
  CALL
  EMAIL
  MEETING
  FOLLOW_UP
  TASK
  OTHER
}
