{"name": "@crm/database", "version": "1.0.0", "description": "Database package with Prisma ORM for CRM application", "main": "index.js", "scripts": {"db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts", "db:reset": "prisma migrate reset"}, "dependencies": {"@prisma/client": "^5.0.0", "prisma": "^5.0.0"}, "devDependencies": {"tsx": "^4.0.0", "@types/node": "^20.0.0"}}