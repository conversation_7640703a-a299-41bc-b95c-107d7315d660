import Joi from 'joi';

// Common schemas
export const paginationSchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(10),
  sortBy: Joi.string().optional(),
  sortOrder: Joi.string().valid('asc', 'desc').default('desc'),
});

export const idSchema = Joi.object({
  id: Joi.string().required(),
});

// Auth schemas
export const loginSchema = Joi.object({
  email: Joi.string().email().required(),
  password: Joi.string().min(6).required(),
});

export const registerSchema = Joi.object({
  email: Joi.string().email().required(),
  password: Joi.string().min(8).required(),
  firstName: Joi.string().min(2).max(50).required(),
  lastName: Joi.string().min(2).max(50).required(),
  role: Joi.string().valid('ADMIN', 'AGENT').default('AGENT'),
});

export const changePasswordSchema = Joi.object({
  currentPassword: Joi.string().required(),
  newPassword: Joi.string().min(8).required(),
});

export const refreshTokenSchema = Joi.object({
  refreshToken: Joi.string().required(),
});

// Lead schemas
export const createLeadSchema = Joi.object({
  firstName: Joi.string().min(2).max(50).required(),
  lastName: Joi.string().min(2).max(50).required(),
  email: Joi.string().email().optional(),
  phone: Joi.string().optional(),
  source: Joi.string().valid(
    'WEBSITE',
    'REFERRAL',
    'SOCIAL_MEDIA',
    'COLD_CALL',
    'EMAIL_CAMPAIGN',
    'WALK_IN',
    'OTHER'
  ).required(),
  assignedTo: Joi.string().optional(),
  description: Joi.string().max(500).optional(),
});

export const updateLeadSchema = Joi.object({
  firstName: Joi.string().min(2).max(50).optional(),
  lastName: Joi.string().min(2).max(50).optional(),
  email: Joi.string().email().optional(),
  phone: Joi.string().optional(),
  source: Joi.string().valid(
    'WEBSITE',
    'REFERRAL',
    'SOCIAL_MEDIA',
    'COLD_CALL',
    'EMAIL_CAMPAIGN',
    'WALK_IN',
    'OTHER'
  ).optional(),
  status: Joi.string().valid(
    'NEW',
    'CONTACTED',
    'QUALIFIED',
    'PROPOSAL_SENT',
    'NEGOTIATING',
    'CLOSED_WON',
    'CLOSED_LOST'
  ).optional(),
  assignedTo: Joi.string().optional(),
  description: Joi.string().max(500).optional(),
});

// Contact schemas
export const createContactSchema = Joi.object({
  leadId: Joi.string().required(),
  firstName: Joi.string().min(2).max(50).required(),
  lastName: Joi.string().min(2).max(50).required(),
  email: Joi.string().email().optional(),
  phone: Joi.string().optional(),
  address: Joi.string().max(200).optional(),
  city: Joi.string().max(100).optional(),
  state: Joi.string().max(50).optional(),
  zipCode: Joi.string().max(20).optional(),
  preferences: Joi.object().optional(),
});

export const updateContactSchema = Joi.object({
  firstName: Joi.string().min(2).max(50).optional(),
  lastName: Joi.string().min(2).max(50).optional(),
  email: Joi.string().email().optional(),
  phone: Joi.string().optional(),
  address: Joi.string().max(200).optional(),
  city: Joi.string().max(100).optional(),
  state: Joi.string().max(50).optional(),
  zipCode: Joi.string().max(20).optional(),
  preferences: Joi.object().optional(),
});

// Property schemas
export const createPropertySchema = Joi.object({
  title: Joi.string().min(5).max(200).required(),
  description: Joi.string().max(1000).optional(),
  address: Joi.string().max(200).required(),
  city: Joi.string().max(100).required(),
  state: Joi.string().max(50).required(),
  zipCode: Joi.string().max(20).required(),
  price: Joi.number().positive().required(),
  bedrooms: Joi.number().integer().min(0).optional(),
  bathrooms: Joi.number().positive().optional(),
  sqft: Joi.number().integer().positive().optional(),
  lotSize: Joi.number().positive().optional(),
  yearBuilt: Joi.number().integer().min(1800).max(new Date().getFullYear()).optional(),
  type: Joi.string().valid(
    'HOUSE',
    'APARTMENT',
    'CONDO',
    'TOWNHOUSE',
    'LAND',
    'COMMERCIAL',
    'OTHER'
  ).required(),
  features: Joi.object().optional(),
  images: Joi.array().items(Joi.string().uri()).optional(),
});

export const updatePropertySchema = Joi.object({
  title: Joi.string().min(5).max(200).optional(),
  description: Joi.string().max(1000).optional(),
  address: Joi.string().max(200).optional(),
  city: Joi.string().max(100).optional(),
  state: Joi.string().max(50).optional(),
  zipCode: Joi.string().max(20).optional(),
  price: Joi.number().positive().optional(),
  bedrooms: Joi.number().integer().min(0).optional(),
  bathrooms: Joi.number().positive().optional(),
  sqft: Joi.number().integer().positive().optional(),
  lotSize: Joi.number().positive().optional(),
  yearBuilt: Joi.number().integer().min(1800).max(new Date().getFullYear()).optional(),
  type: Joi.string().valid(
    'HOUSE',
    'APARTMENT',
    'CONDO',
    'TOWNHOUSE',
    'LAND',
    'COMMERCIAL',
    'OTHER'
  ).optional(),
  status: Joi.string().valid(
    'AVAILABLE',
    'UNDER_CONTRACT',
    'SOLD',
    'OFF_MARKET'
  ).optional(),
  features: Joi.object().optional(),
  images: Joi.array().items(Joi.string().uri()).optional(),
});

// Deal schemas
export const createDealSchema = Joi.object({
  leadId: Joi.string().required(),
  contactId: Joi.string().optional(),
  propertyId: Joi.string().optional(),
  value: Joi.number().positive().required(),
  stage: Joi.string().max(100).required(),
  closeDate: Joi.date().optional(),
  notes: Joi.string().max(1000).optional(),
});

export const updateDealSchema = Joi.object({
  contactId: Joi.string().optional(),
  propertyId: Joi.string().optional(),
  value: Joi.number().positive().optional(),
  status: Joi.string().valid(
    'PROSPECTING',
    'QUALIFICATION',
    'PROPOSAL',
    'NEGOTIATION',
    'CLOSING',
    'CLOSED_WON',
    'CLOSED_LOST'
  ).optional(),
  stage: Joi.string().max(100).optional(),
  closeDate: Joi.date().optional(),
  notes: Joi.string().max(1000).optional(),
});

// Note schemas
export const createNoteSchema = Joi.object({
  content: Joi.string().min(1).max(2000).required(),
  leadId: Joi.string().optional(),
  dealId: Joi.string().optional(),
}).xor('leadId', 'dealId');

export const updateNoteSchema = Joi.object({
  content: Joi.string().min(1).max(2000).required(),
});

// Reminder schemas
export const createReminderSchema = Joi.object({
  title: Joi.string().min(3).max(200).required(),
  description: Joi.string().max(500).optional(),
  dueDate: Joi.date().min('now').required(),
  type: Joi.string().valid(
    'CALL',
    'EMAIL',
    'MEETING',
    'FOLLOW_UP',
    'TASK',
    'OTHER'
  ).required(),
  leadId: Joi.string().optional(),
  dealId: Joi.string().optional(),
});

export const updateReminderSchema = Joi.object({
  title: Joi.string().min(3).max(200).optional(),
  description: Joi.string().max(500).optional(),
  dueDate: Joi.date().min('now').optional(),
  isCompleted: Joi.boolean().optional(),
  type: Joi.string().valid(
    'CALL',
    'EMAIL',
    'MEETING',
    'FOLLOW_UP',
    'TASK',
    'OTHER'
  ).optional(),
});
