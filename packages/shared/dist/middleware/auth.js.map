{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../src/middleware/auth.ts"], "names": [], "mappings": ";;;AACA,sCAAyC;AACzC,gDAAmD;AAEnD,4CAAuC;AAEhC,MAAM,iBAAiB,GAAG,KAAK,EACpC,GAAyB,EACzB,GAAa,EACb,IAAkB,EACH,EAAE;IACjB,IAAI,CAAC;QACH,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC;QAC7C,MAAM,KAAK,GAAG,UAAU,IAAI,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe;QAErE,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,yBAAc,CAAC,YAAY,CAAC,GAAG,EAAE,uBAAuB,CAAC,CAAC;YAC1D,OAAO;QACT,CAAC;QAED,MAAM,OAAO,GAAG,eAAS,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;QAEnD,qEAAqE;QACrE,MAAM,IAAI,GAAG,MAAM,iBAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,MAAM,EAAE;YAC7B,MAAM,EAAE;gBACN,EAAE,EAAE,IAAI;gBACR,KAAK,EAAE,IAAI;gBACX,SAAS,EAAE,IAAI;gBACf,QAAQ,EAAE,IAAI;gBACd,IAAI,EAAE,IAAI;gBACV,QAAQ,EAAE,IAAI;aACf;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC5B,yBAAc,CAAC,YAAY,CAAC,GAAG,EAAE,0BAA0B,CAAC,CAAC;YAC7D,OAAO;QACT,CAAC;QAED,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;QAChB,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,yBAAc,CAAC,YAAY,CAAC,GAAG,EAAE,sBAAsB,CAAC,CAAC;IAC3D,CAAC;AACH,CAAC,CAAC;AAvCW,QAAA,iBAAiB,qBAuC5B;AAEK,MAAM,WAAW,GAAG,CAAC,KAAwB,EAAE,EAAE;IACtD,OAAO,CAAC,GAAyB,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;QAC5E,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YACd,yBAAc,CAAC,YAAY,CAAC,GAAG,EAAE,yBAAyB,CAAC,CAAC;YAC5D,OAAO;QACT,CAAC;QAED,MAAM,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QAE5D,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YAC1C,yBAAc,CAAC,SAAS,CAAC,GAAG,EAAE,0BAA0B,CAAC,CAAC;YAC1D,OAAO;QACT,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC,CAAC;AAhBW,QAAA,WAAW,eAgBtB;AAEW,QAAA,YAAY,GAAG,IAAA,mBAAW,EAAC,OAAO,CAAC,CAAC;AACpC,QAAA,YAAY,GAAG,IAAA,mBAAW,EAAC,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC"}