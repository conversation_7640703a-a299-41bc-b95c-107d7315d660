import { RedisClientType } from 'redis';
declare class RedisClient {
    private client;
    private isConnected;
    constructor();
    connect(): Promise<void>;
    disconnect(): Promise<void>;
    set(key: string, value: string, ttl?: number): Promise<void>;
    get(key: string): Promise<string | null>;
    del(key: string): Promise<number>;
    exists(key: string): Promise<number>;
    setJson(key: string, value: any, ttl?: number): Promise<void>;
    getJson<T>(key: string): Promise<T | null>;
    incr(key: string): Promise<number>;
    expire(key: string, seconds: number): Promise<boolean>;
    keys(pattern: string): Promise<string[]>;
    private ensureConnected;
    getClient(): RedisClientType;
}
export declare const redisClient: RedisClient;
export { RedisClient };
//# sourceMappingURL=redis.d.ts.map