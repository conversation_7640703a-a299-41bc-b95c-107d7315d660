import { JWTPayload } from '../types/auth';
export declare class JWTHelper {
    private static readonly ACCESS_TOKEN_SECRET;
    private static readonly REFRESH_TOKEN_SECRET;
    private static readonly ACCESS_TOKEN_EXPIRES_IN;
    private static readonly REFRESH_TOKEN_EXPIRES_IN;
    static generateAccessToken(payload: Omit<JWTPayload, 'iat' | 'exp'>): string;
    static generateRefreshToken(payload: Omit<JWTPayload, 'iat' | 'exp'>): string;
    static verifyAccessToken(token: string): JWTPayload;
    static verifyRefreshToken(token: string): JWTPayload;
    static generateTokenPair(payload: Omit<JWTPayload, 'iat' | 'exp'>): {
        accessToken: string;
        refreshToken: string;
    };
}
//# sourceMappingURL=jwt.d.ts.map