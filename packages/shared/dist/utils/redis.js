"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RedisClient = exports.redisClient = void 0;
const redis_1 = require("redis");
const logger_1 = require("./logger");
class RedisClient {
    constructor() {
        this.isConnected = false;
        this.client = (0, redis_1.createClient)({
            url: process.env.REDIS_URL || 'redis://localhost:6379',
        });
        this.client.on('error', (err) => {
            logger_1.logger.error('Redis Client Error:', err);
        });
        this.client.on('connect', () => {
            logger_1.logger.info('Redis Client Connected');
            this.isConnected = true;
        });
        this.client.on('disconnect', () => {
            logger_1.logger.warn('Redis Client Disconnected');
            this.isConnected = false;
        });
    }
    async connect() {
        if (!this.isConnected) {
            await this.client.connect();
        }
    }
    async disconnect() {
        if (this.isConnected) {
            await this.client.disconnect();
        }
    }
    async set(key, value, ttl) {
        await this.ensureConnected();
        if (ttl) {
            await this.client.setEx(key, ttl, value);
        }
        else {
            await this.client.set(key, value);
        }
    }
    async get(key) {
        await this.ensureConnected();
        return this.client.get(key);
    }
    async del(key) {
        await this.ensureConnected();
        return this.client.del(key);
    }
    async exists(key) {
        await this.ensureConnected();
        return this.client.exists(key);
    }
    async setJson(key, value, ttl) {
        await this.set(key, JSON.stringify(value), ttl);
    }
    async getJson(key) {
        const value = await this.get(key);
        return value ? JSON.parse(value) : null;
    }
    async incr(key) {
        await this.ensureConnected();
        return this.client.incr(key);
    }
    async expire(key, seconds) {
        await this.ensureConnected();
        return this.client.expire(key, seconds);
    }
    async keys(pattern) {
        await this.ensureConnected();
        return this.client.keys(pattern);
    }
    async ensureConnected() {
        if (!this.isConnected) {
            await this.connect();
        }
    }
    getClient() {
        return this.client;
    }
}
exports.RedisClient = RedisClient;
exports.redisClient = new RedisClient();
//# sourceMappingURL=redis.js.map