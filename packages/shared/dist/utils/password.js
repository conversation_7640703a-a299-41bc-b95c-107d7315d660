"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PasswordHelper = void 0;
const bcryptjs_1 = __importDefault(require("bcryptjs"));
class PasswordHelper {
    static async hash(password) {
        return bcryptjs_1.default.hash(password, this.SALT_ROUNDS);
    }
    static async compare(password, hashedPassword) {
        return bcryptjs_1.default.compare(password, hashedPassword);
    }
    static validatePasswordStrength(password) {
        const errors = [];
        if (password.length < 8) {
            errors.push('Password must be at least 8 characters long');
        }
        if (!/[A-Z]/.test(password)) {
            errors.push('Password must contain at least one uppercase letter');
        }
        if (!/[a-z]/.test(password)) {
            errors.push('Password must contain at least one lowercase letter');
        }
        if (!/\d/.test(password)) {
            errors.push('Password must contain at least one number');
        }
        if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
            errors.push('Password must contain at least one special character');
        }
        return {
            isValid: errors.length === 0,
            errors,
        };
    }
}
exports.PasswordHelper = PasswordHelper;
PasswordHelper.SALT_ROUNDS = 12;
//# sourceMappingURL=password.js.map