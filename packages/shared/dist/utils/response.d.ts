import { Response } from 'express';
import { SuccessResponse, ErrorResponse } from '../types/api';
export declare class ResponseHelper {
    static success<T>(res: Response, data: T, message?: string, statusCode?: number): Response<SuccessResponse<T>>;
    static error(res: Response, error: string, statusCode?: number, message?: string, details?: any): Response<ErrorResponse>;
    static badRequest(res: Response, message?: string, details?: any): Response<ErrorResponse>;
    static unauthorized(res: Response, message?: string): Response<ErrorResponse>;
    static forbidden(res: Response, message?: string): Response<ErrorResponse>;
    static notFound(res: Response, message?: string): Response<ErrorResponse>;
    static conflict(res: Response, message?: string): Response<ErrorResponse>;
    static validationError(res: Response, details: any, message?: string): Response<ErrorResponse>;
    static internalServerError(res: Response, message?: string): Response<ErrorResponse>;
}
//# sourceMappingURL=response.d.ts.map