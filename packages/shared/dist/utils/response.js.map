{"version": 3, "file": "response.js", "sourceRoot": "", "sources": ["../../src/utils/response.ts"], "names": [], "mappings": ";;;AAGA,MAAa,cAAc;IACzB,MAAM,CAAC,OAAO,CACZ,GAAa,EACb,IAAO,EACP,OAAgB,EAChB,aAAqB,GAAG;QAExB,MAAM,QAAQ,GAAuB;YACnC,OAAO,EAAE,IAAI;YACb,IAAI;YACJ,OAAO;YACP,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,WAAW;SAC1B,CAAC;QAEF,OAAO,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC/C,CAAC;IAED,MAAM,CAAC,KAAK,CACV,GAAa,EACb,KAAa,EACb,aAAqB,GAAG,EACxB,OAAgB,EAChB,OAAa;QAEb,MAAM,QAAQ,GAAkB;YAC9B,OAAO,EAAE,KAAK;YACd,KAAK;YACL,OAAO;YACP,UAAU;YACV,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,WAAW;YACzB,OAAO;SACR,CAAC;QAEF,OAAO,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC/C,CAAC;IAED,MAAM,CAAC,UAAU,CACf,GAAa,EACb,UAAkB,aAAa,EAC/B,OAAa;QAEb,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,aAAa,EAAE,GAAG,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAC/D,CAAC;IAED,MAAM,CAAC,YAAY,CACjB,GAAa,EACb,UAAkB,cAAc;QAEhC,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,cAAc,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;IACvD,CAAC;IAED,MAAM,CAAC,SAAS,CACd,GAAa,EACb,UAAkB,WAAW;QAE7B,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,WAAW,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;IACpD,CAAC;IAED,MAAM,CAAC,QAAQ,CACb,GAAa,EACb,UAAkB,oBAAoB;QAEtC,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,WAAW,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;IACpD,CAAC;IAED,MAAM,CAAC,QAAQ,CACb,GAAa,EACb,UAAkB,UAAU;QAE5B,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,UAAU,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;IACnD,CAAC;IAED,MAAM,CAAC,eAAe,CACpB,GAAa,EACb,OAAY,EACZ,UAAkB,mBAAmB;QAErC,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,kBAAkB,EAAE,GAAG,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACpE,CAAC;IAED,MAAM,CAAC,mBAAmB,CACxB,GAAa,EACb,UAAkB,uBAAuB;QAEzC,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,uBAAuB,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;IAChE,CAAC;CACF;AAxFD,wCAwFC"}