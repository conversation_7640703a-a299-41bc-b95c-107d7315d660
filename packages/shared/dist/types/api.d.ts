import { Request } from 'express';
import { AuthenticatedUser } from './auth';
export interface AuthenticatedRequest extends Request {
    user?: AuthenticatedUser;
}
export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    message?: string;
    error?: string;
    timestamp: string;
    path: string;
}
export interface ErrorResponse {
    success: false;
    error: string;
    message?: string;
    statusCode: number;
    timestamp: string;
    path: string;
    details?: any;
}
export interface SuccessResponse<T = any> {
    success: true;
    data: T;
    message?: string;
    timestamp: string;
    path: string;
}
export interface ValidationError {
    field: string;
    message: string;
    value?: any;
}
export interface ValidationErrorResponse extends ErrorResponse {
    details: ValidationError[];
}
//# sourceMappingURL=api.d.ts.map