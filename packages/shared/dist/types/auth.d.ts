export interface JWTPayload {
    userId: string;
    email: string;
    role: string;
    iat?: number;
    exp?: number;
}
export interface AuthenticatedUser {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    role: string;
    isActive: boolean;
}
export interface LoginRequest {
    email: string;
    password: string;
}
export interface LoginResponse {
    user: AuthenticatedUser;
    accessToken: string;
    refreshToken: string;
}
export interface RegisterRequest {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    role?: 'ADMIN' | 'AGENT';
}
export interface RefreshTokenRequest {
    refreshToken: string;
}
export interface ChangePasswordRequest {
    currentPassword: string;
    newPassword: string;
}
//# sourceMappingURL=auth.d.ts.map