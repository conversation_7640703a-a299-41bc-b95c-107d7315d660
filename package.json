{"name": "real-estate-crm", "version": "1.0.0", "description": "A comprehensive CRM application for real estate management", "private": true, "workspaces": ["services/*", "packages/*"], "scripts": {"dev": "concurrently \"npm run dev:gateway\" \"npm run dev:auth\" \"npm run dev:lead\" \"npm run dev:contact\" \"npm run dev:property\" \"npm run dev:deal\" \"npm run dev:notification\"", "dev:gateway": "npm run dev --workspace=services/api-gateway", "dev:auth": "npm run dev --workspace=services/auth-service", "dev:lead": "npm run dev --workspace=services/lead-service", "dev:contact": "npm run dev --workspace=services/contact-service", "dev:property": "npm run dev --workspace=services/property-service", "dev:deal": "npm run dev --workspace=services/deal-service", "dev:notification": "npm run dev --workspace=services/notification-service", "build": "npm run build --workspaces", "start": "npm run start --workspaces", "test": "npm run test --workspaces", "db:generate": "npm run db:generate --workspace=packages/database", "db:push": "npm run db:push --workspace=packages/database", "db:migrate": "npm run db:migrate --workspace=packages/database", "db:studio": "npm run db:studio --workspace=packages/database", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "install:all": "npm install && npm install --workspaces"}, "devDependencies": {"@types/node": "^20.0.0", "concurrently": "^8.2.0", "nodemon": "^3.0.0", "typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0"}, "keywords": ["crm", "real-estate", "microservices", "nodejs", "express", "postgresql", "redis"], "author": "Real Estate CRM Team", "license": "MIT"}