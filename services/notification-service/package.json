{"name": "@crm/notification-service", "version": "1.0.0", "description": "Notification and reminder service for CRM application", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "clean": "rm -rf dist"}, "dependencies": {"@crm/shared": "*", "@crm/database": "*", "express": "^4.18.0", "dotenv": "^16.3.0", "node-cron": "^3.0.2", "nodemailer": "^6.9.0"}, "devDependencies": {"@types/express": "^4.17.0", "@types/node": "^20.0.0", "@types/node-cron": "^3.0.0", "@types/nodemailer": "^6.4.0", "nodemon": "^3.0.0", "ts-node": "^10.9.0", "typescript": "^5.0.0", "jest": "^29.0.0", "@types/jest": "^29.0.0"}}