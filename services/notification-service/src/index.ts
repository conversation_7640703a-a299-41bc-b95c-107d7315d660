import express from 'express';
import dotenv from 'dotenv';
import cron from 'node-cron';
import {
  corsMiddleware,
  developmentCors,
  errorHandler,
  notFoundHandler,
  apiLimiter,
  createServiceLogger,
} from '@crm/shared';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.NOTIFICATION_SERVICE_PORT || 3006;
const logger = createServiceLogger('notification-service');

// Middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// CORS
if (process.env.NODE_ENV === 'development') {
  app.use(developmentCors);
} else {
  app.use(corsMiddleware);
}

// Rate limiting
app.use(apiLimiter);

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'notification-service',
    version: '1.0.0',
  });
});

// TODO: Add notification routes
app.get('/', (req, res) => {
  res.json({
    message: 'Notification Service - Coming Soon',
    endpoints: {
      health: '/health',
      reminders: '/ (CRUD operations)',
      notifications: '/notifications',
    },
  });
});

// TODO: Set up cron jobs for reminder notifications
// Example: Check for due reminders every 5 minutes
cron.schedule('*/5 * * * *', () => {
  logger.info('Checking for due reminders...');
  // TODO: Implement reminder checking logic
});

// Error handling
app.use(notFoundHandler);
app.use(errorHandler);

// Start server
app.listen(PORT, () => {
  logger.info(`🔔 Notification Service running on port ${PORT}`);
  logger.info('📅 Reminder cron job scheduled (every 5 minutes)');
});

export default app;
