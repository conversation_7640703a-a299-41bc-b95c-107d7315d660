import request from 'supertest';
import app from '../index';
import { prisma } from '@crm/database';
import { PasswordHelper } from '@crm/shared';

describe('Auth Service', () => {
  beforeAll(async () => {
    // Setup test database
    await prisma.$connect();
  });

  afterAll(async () => {
    // Cleanup test database
    await prisma.user.deleteMany();
    await prisma.$disconnect();
  });

  beforeEach(async () => {
    // Clean up before each test
    await prisma.user.deleteMany();
  });

  describe('POST /login', () => {
    beforeEach(async () => {
      // Create a test user
      const hashedPassword = await PasswordHelper.hash('testpassword123');
      await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: hashedPassword,
          firstName: 'Test',
          lastName: 'User',
          role: 'AGENT',
        },
      });
    });

    it('should login with valid credentials', async () => {
      const response = await request(app)
        .post('/login')
        .send({
          email: '<EMAIL>',
          password: 'testpassword123',
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('accessToken');
      expect(response.body.data).toHaveProperty('refreshToken');
      expect(response.body.data.user).toHaveProperty('email', '<EMAIL>');
    });

    it('should reject invalid credentials', async () => {
      const response = await request(app)
        .post('/login')
        .send({
          email: '<EMAIL>',
          password: 'wrongpassword',
        });

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
    });

    it('should reject non-existent user', async () => {
      const response = await request(app)
        .post('/login')
        .send({
          email: '<EMAIL>',
          password: 'testpassword123',
        });

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
    });
  });

  describe('GET /health', () => {
    it('should return health status', async () => {
      const response = await request(app).get('/health');

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('status', 'healthy');
      expect(response.body).toHaveProperty('service', 'auth-service');
    });
  });
});
