{"clientVersion":"5.22.0","level":"error","message":"Login service error: \nInvalid `prisma.user.findUnique()` invocation in\n/Users/<USER>/Desktop/real_state_crm_backend/services/auth-service/src/services/authService.ts:20:36\n\n  17 ): Promise<ServiceResponse<LoginResponse>> => {\n  18   try {\n  19     // Find user by email\n→ 20     const user = await prisma.user.findUnique(\nerror: Environment variable not found: DATABASE_URL.\n  -->  schema.prisma:10\n   | \n 9 |   provider = \"postgresql\"\n10 |   url      = env(\"DATABASE_URL\")\n   | \n\nValidation Error Count: 1","name":"PrismaClientInitializationError","service":"crm-service","stack":"PrismaClientInitializationError: \nInvalid `prisma.user.findUnique()` invocation in\n/Users/<USER>/Desktop/real_state_crm_backend/services/auth-service/src/services/authService.ts:20:36\n\n  17 ): Promise<ServiceResponse<LoginResponse>> => {\n  18   try {\n  19     // Find user by email\n→ 20     const user = await prisma.user.findUnique(\nerror: Environment variable not found: DATABASE_URL.\n  -->  schema.prisma:10\n   | \n 9 |   provider = \"postgresql\"\n10 |   url      = env(\"DATABASE_URL\")\n   | \n\nValidation Error Count: 1\n    at $n.handleRequestError (/Users/<USER>/Desktop/real_state_crm_backend/node_modules/@prisma/client/runtime/library.js:121:7615)\n    at $n.handleAndLogRequestError (/Users/<USER>/Desktop/real_state_crm_backend/node_modules/@prisma/client/runtime/library.js:121:6623)\n    at $n.request (/Users/<USER>/Desktop/real_state_crm_backend/node_modules/@prisma/client/runtime/library.js:121:6307)\n    at async l (/Users/<USER>/Desktop/real_state_crm_backend/node_modules/@prisma/client/runtime/library.js:130:9633)\n    at async Object.loginUser (/Users/<USER>/Desktop/real_state_crm_backend/services/auth-service/src/services/authService.ts:20:18)\n    at async login (/Users/<USER>/Desktop/real_state_crm_backend/services/auth-service/src/controllers/authController.ts:19:20)","timestamp":"2025-05-30T21:47:09.723Z"}
{"clientVersion":"5.22.0","level":"error","message":"Login service error: \nInvalid `prisma.user.findUnique()` invocation in\n/Users/<USER>/Desktop/real_state_crm_backend/services/auth-service/src/services/authService.ts:20:36\n\n  17 ): Promise<ServiceResponse<LoginResponse>> => {\n  18   try {\n  19     // Find user by email\n→ 20     const user = await prisma.user.findUnique(\nerror: Environment variable not found: DATABASE_URL.\n  -->  schema.prisma:10\n   | \n 9 |   provider = \"postgresql\"\n10 |   url      = env(\"DATABASE_URL\")\n   | \n\nValidation Error Count: 1","name":"PrismaClientInitializationError","service":"crm-service","stack":"PrismaClientInitializationError: \nInvalid `prisma.user.findUnique()` invocation in\n/Users/<USER>/Desktop/real_state_crm_backend/services/auth-service/src/services/authService.ts:20:36\n\n  17 ): Promise<ServiceResponse<LoginResponse>> => {\n  18   try {\n  19     // Find user by email\n→ 20     const user = await prisma.user.findUnique(\nerror: Environment variable not found: DATABASE_URL.\n  -->  schema.prisma:10\n   | \n 9 |   provider = \"postgresql\"\n10 |   url      = env(\"DATABASE_URL\")\n   | \n\nValidation Error Count: 1\n    at $n.handleRequestError (/Users/<USER>/Desktop/real_state_crm_backend/node_modules/@prisma/client/runtime/library.js:121:7615)\n    at $n.handleAndLogRequestError (/Users/<USER>/Desktop/real_state_crm_backend/node_modules/@prisma/client/runtime/library.js:121:6623)\n    at $n.request (/Users/<USER>/Desktop/real_state_crm_backend/node_modules/@prisma/client/runtime/library.js:121:6307)\n    at async l (/Users/<USER>/Desktop/real_state_crm_backend/node_modules/@prisma/client/runtime/library.js:130:9633)\n    at async Object.loginUser (/Users/<USER>/Desktop/real_state_crm_backend/services/auth-service/src/services/authService.ts:20:18)\n    at async login (/Users/<USER>/Desktop/real_state_crm_backend/services/auth-service/src/controllers/authController.ts:19:20)","timestamp":"2025-05-30T21:47:27.565Z"}
{"clientVersion":"5.22.0","level":"error","message":"Login service error: \nInvalid `prisma.user.findUnique()` invocation in\n/Users/<USER>/Desktop/real_state_crm_backend/services/auth-service/src/services/authService.ts:20:36\n\n  17 ): Promise<ServiceResponse<LoginResponse>> => {\n  18   try {\n  19     // Find user by email\n→ 20     const user = await prisma.user.findUnique(\nerror: Environment variable not found: DATABASE_URL.\n  -->  schema.prisma:10\n   | \n 9 |   provider = \"postgresql\"\n10 |   url      = env(\"DATABASE_URL\")\n   | \n\nValidation Error Count: 1","name":"PrismaClientInitializationError","service":"crm-service","stack":"PrismaClientInitializationError: \nInvalid `prisma.user.findUnique()` invocation in\n/Users/<USER>/Desktop/real_state_crm_backend/services/auth-service/src/services/authService.ts:20:36\n\n  17 ): Promise<ServiceResponse<LoginResponse>> => {\n  18   try {\n  19     // Find user by email\n→ 20     const user = await prisma.user.findUnique(\nerror: Environment variable not found: DATABASE_URL.\n  -->  schema.prisma:10\n   | \n 9 |   provider = \"postgresql\"\n10 |   url      = env(\"DATABASE_URL\")\n   | \n\nValidation Error Count: 1\n    at $n.handleRequestError (/Users/<USER>/Desktop/real_state_crm_backend/node_modules/@prisma/client/runtime/library.js:121:7615)\n    at $n.handleAndLogRequestError (/Users/<USER>/Desktop/real_state_crm_backend/node_modules/@prisma/client/runtime/library.js:121:6623)\n    at $n.request (/Users/<USER>/Desktop/real_state_crm_backend/node_modules/@prisma/client/runtime/library.js:121:6307)\n    at async l (/Users/<USER>/Desktop/real_state_crm_backend/node_modules/@prisma/client/runtime/library.js:130:9633)\n    at async Object.loginUser (/Users/<USER>/Desktop/real_state_crm_backend/services/auth-service/src/services/authService.ts:20:18)\n    at async login (/Users/<USER>/Desktop/real_state_crm_backend/services/auth-service/src/controllers/authController.ts:19:20)","timestamp":"2025-05-30T21:47:39.423Z"}
