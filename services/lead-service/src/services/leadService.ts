import {
  ServiceResponse,
  PaginatedResponse,
  PaginationParams,
  createServiceLogger,
} from '@crm/shared';
import { prisma, Lead, LeadStatus } from '@crm/database';

const logger = createServiceLogger('lead-service');

interface LeadFilters extends PaginationParams {
  userId?: string; // For filtering by assigned agent
  status?: LeadStatus;
  source?: string;
}

export const getLeads = async (
  filters: LeadFilters
): Promise<ServiceResponse<PaginatedResponse<Lead>>> => {
  try {
    const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = 'desc', userId, status, source } = filters;
    
    const skip = (page - 1) * limit;
    
    // Build where clause
    const where: any = {};
    if (userId) {
      where.assignedTo = userId;
    }
    if (status) {
      where.status = status;
    }
    if (source) {
      where.source = source;
    }

    // Get total count
    const total = await prisma.lead.count({ where });

    // Get leads with pagination
    const leads = await prisma.lead.findMany({
      where,
      skip,
      take: limit,
      orderBy: {
        [sortBy]: sortOrder,
      },
      include: {
        assignedAgent: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        contact: true,
        _count: {
          select: {
            deals: true,
            notes: true,
            reminders: true,
          },
        },
      },
    });

    const totalPages = Math.ceil(total / limit);

    return {
      success: true,
      data: {
        data: leads,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
      },
    };
  } catch (error) {
    logger.error('Get leads service error:', error);
    return {
      success: false,
      message: 'Failed to fetch leads',
      statusCode: 500,
    };
  }
};

export const getLeadById = async (
  id: string,
  userId?: string
): Promise<ServiceResponse<Lead>> => {
  try {
    const where: any = { id };
    if (userId) {
      where.assignedTo = userId;
    }

    const lead = await prisma.lead.findFirst({
      where,
      include: {
        assignedAgent: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        contact: true,
        deals: {
          include: {
            property: true,
          },
        },
        notes: {
          include: {
            author: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
              },
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
        },
        reminders: {
          where: {
            isCompleted: false,
          },
          orderBy: {
            dueDate: 'asc',
          },
        },
      },
    });

    if (!lead) {
      return {
        success: false,
        message: 'Lead not found',
        statusCode: 404,
      };
    }

    return {
      success: true,
      data: lead,
    };
  } catch (error) {
    logger.error('Get lead by ID service error:', error);
    return {
      success: false,
      message: 'Failed to fetch lead',
      statusCode: 500,
    };
  }
};

export const createLead = async (
  leadData: any,
  createdBy: string
): Promise<ServiceResponse<Lead>> => {
  try {
    // Check if assigned agent exists (if provided)
    if (leadData.assignedTo) {
      const agent = await prisma.user.findUnique({
        where: { id: leadData.assignedTo },
      });

      if (!agent || !agent.isActive) {
        return {
          success: false,
          message: 'Assigned agent not found or inactive',
          statusCode: 400,
        };
      }
    }

    const lead = await prisma.lead.create({
      data: {
        ...leadData,
        assignedTo: leadData.assignedTo || createdBy, // Assign to creator if no agent specified
      },
      include: {
        assignedAgent: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });

    return {
      success: true,
      data: lead,
    };
  } catch (error) {
    logger.error('Create lead service error:', error);
    return {
      success: false,
      message: 'Failed to create lead',
      statusCode: 500,
    };
  }
};

export const updateLead = async (
  id: string,
  updateData: any,
  userId?: string
): Promise<ServiceResponse<Lead>> => {
  try {
    const where: any = { id };
    if (userId) {
      where.assignedTo = userId;
    }

    // Check if lead exists and user has access
    const existingLead = await prisma.lead.findFirst({ where });
    if (!existingLead) {
      return {
        success: false,
        message: 'Lead not found or access denied',
        statusCode: 404,
      };
    }

    // Check if assigned agent exists (if being updated)
    if (updateData.assignedTo) {
      const agent = await prisma.user.findUnique({
        where: { id: updateData.assignedTo },
      });

      if (!agent || !agent.isActive) {
        return {
          success: false,
          message: 'Assigned agent not found or inactive',
          statusCode: 400,
        };
      }
    }

    const lead = await prisma.lead.update({
      where: { id },
      data: updateData,
      include: {
        assignedAgent: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });

    return {
      success: true,
      data: lead,
    };
  } catch (error) {
    logger.error('Update lead service error:', error);
    return {
      success: false,
      message: 'Failed to update lead',
      statusCode: 500,
    };
  }
};

export const deleteLead = async (
  id: string,
  userId?: string
): Promise<ServiceResponse<null>> => {
  try {
    const where: any = { id };
    if (userId) {
      where.assignedTo = userId;
    }

    // Check if lead exists and user has access
    const existingLead = await prisma.lead.findFirst({ where });
    if (!existingLead) {
      return {
        success: false,
        message: 'Lead not found or access denied',
        statusCode: 404,
      };
    }

    await prisma.lead.delete({
      where: { id },
    });

    return {
      success: true,
      data: null,
    };
  } catch (error) {
    logger.error('Delete lead service error:', error);
    return {
      success: false,
      message: 'Failed to delete lead',
      statusCode: 500,
    };
  }
};

export const assignLead = async (
  id: string,
  assignedTo: string
): Promise<ServiceResponse<Lead>> => {
  try {
    // Check if lead exists
    const existingLead = await prisma.lead.findUnique({
      where: { id },
    });

    if (!existingLead) {
      return {
        success: false,
        message: 'Lead not found',
        statusCode: 404,
      };
    }

    // Check if assigned agent exists
    const agent = await prisma.user.findUnique({
      where: { id: assignedTo },
    });

    if (!agent || !agent.isActive) {
      return {
        success: false,
        message: 'Assigned agent not found or inactive',
        statusCode: 400,
      };
    }

    const lead = await prisma.lead.update({
      where: { id },
      data: { assignedTo },
      include: {
        assignedAgent: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });

    return {
      success: true,
      data: lead,
    };
  } catch (error) {
    logger.error('Assign lead service error:', error);
    return {
      success: false,
      message: 'Failed to assign lead',
      statusCode: 500,
    };
  }
};

export const updateLeadStatus = async (
  id: string,
  status: LeadStatus,
  userId?: string
): Promise<ServiceResponse<Lead>> => {
  try {
    const where: any = { id };
    if (userId) {
      where.assignedTo = userId;
    }

    // Check if lead exists and user has access
    const existingLead = await prisma.lead.findFirst({ where });
    if (!existingLead) {
      return {
        success: false,
        message: 'Lead not found or access denied',
        statusCode: 404,
      };
    }

    const lead = await prisma.lead.update({
      where: { id },
      data: { status },
      include: {
        assignedAgent: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });

    return {
      success: true,
      data: lead,
    };
  } catch (error) {
    logger.error('Update lead status service error:', error);
    return {
      success: false,
      message: 'Failed to update lead status',
      statusCode: 500,
    };
  }
};
