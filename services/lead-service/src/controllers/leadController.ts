import { Response } from 'express';
import {
  AuthenticatedRequest,
  ResponseHelper,
  createServiceLogger,
} from '@crm/shared';
import * as leadService from '../services/leadService';

const logger = createServiceLogger('lead-controller');

export const getLeads = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = 'desc' } = req.query as any;
    const userId = req.user!.id;
    const userRole = req.user!.role;

    const result = await leadService.getLeads({
      page: parseInt(page),
      limit: parseInt(limit),
      sortBy,
      sortOrder,
      userId: userRole === 'AGENT' ? userId : undefined,
    });

    if (!result.success) {
      ResponseHelper.badRequest(res, result.message);
      return;
    }

    ResponseHelper.success(res, result.data);
  } catch (error) {
    logger.error('Get leads error:', error);
    ResponseHelper.internalServerError(res, 'Failed to fetch leads');
  }
};

export const getLeadById = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const userId = req.user!.id;
    const userRole = req.user!.role;

    const result = await leadService.getLeadById(id, userRole === 'AGENT' ? userId : undefined);

    if (!result.success) {
      if (result.statusCode === 404) {
        ResponseHelper.notFound(res, result.message);
      } else {
        ResponseHelper.badRequest(res, result.message);
      }
      return;
    }

    ResponseHelper.success(res, result.data);
  } catch (error) {
    logger.error('Get lead by ID error:', error);
    ResponseHelper.internalServerError(res, 'Failed to fetch lead');
  }
};

export const createLead = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const leadData = req.body;
    const createdBy = req.user!.id;

    const result = await leadService.createLead(leadData, createdBy);

    if (!result.success) {
      ResponseHelper.badRequest(res, result.message);
      return;
    }

    logger.info(`Lead created: ${result.data?.id}`);
    ResponseHelper.success(res, result.data, 'Lead created successfully', 201);
  } catch (error) {
    logger.error('Create lead error:', error);
    ResponseHelper.internalServerError(res, 'Failed to create lead');
  }
};

export const updateLead = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    const userId = req.user!.id;
    const userRole = req.user!.role;

    const result = await leadService.updateLead(id, updateData, userRole === 'AGENT' ? userId : undefined);

    if (!result.success) {
      if (result.statusCode === 404) {
        ResponseHelper.notFound(res, result.message);
      } else {
        ResponseHelper.badRequest(res, result.message);
      }
      return;
    }

    logger.info(`Lead updated: ${id}`);
    ResponseHelper.success(res, result.data, 'Lead updated successfully');
  } catch (error) {
    logger.error('Update lead error:', error);
    ResponseHelper.internalServerError(res, 'Failed to update lead');
  }
};

export const deleteLead = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const userId = req.user!.id;
    const userRole = req.user!.role;

    const result = await leadService.deleteLead(id, userRole === 'AGENT' ? userId : undefined);

    if (!result.success) {
      if (result.statusCode === 404) {
        ResponseHelper.notFound(res, result.message);
      } else {
        ResponseHelper.badRequest(res, result.message);
      }
      return;
    }

    logger.info(`Lead deleted: ${id}`);
    ResponseHelper.success(res, null, 'Lead deleted successfully');
  } catch (error) {
    logger.error('Delete lead error:', error);
    ResponseHelper.internalServerError(res, 'Failed to delete lead');
  }
};

export const assignLead = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const { assignedTo } = req.body;

    const result = await leadService.assignLead(id, assignedTo);

    if (!result.success) {
      if (result.statusCode === 404) {
        ResponseHelper.notFound(res, result.message);
      } else {
        ResponseHelper.badRequest(res, result.message);
      }
      return;
    }

    logger.info(`Lead assigned: ${id} to ${assignedTo}`);
    ResponseHelper.success(res, result.data, 'Lead assigned successfully');
  } catch (error) {
    logger.error('Assign lead error:', error);
    ResponseHelper.internalServerError(res, 'Failed to assign lead');
  }
};

export const updateLeadStatus = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const { status } = req.body;
    const userId = req.user!.id;
    const userRole = req.user!.role;

    const result = await leadService.updateLeadStatus(id, status, userRole === 'AGENT' ? userId : undefined);

    if (!result.success) {
      if (result.statusCode === 404) {
        ResponseHelper.notFound(res, result.message);
      } else {
        ResponseHelper.badRequest(res, result.message);
      }
      return;
    }

    logger.info(`Lead status updated: ${id} to ${status}`);
    ResponseHelper.success(res, result.data, 'Lead status updated successfully');
  } catch (error) {
    logger.error('Update lead status error:', error);
    ResponseHelper.internalServerError(res, 'Failed to update lead status');
  }
};
