import express from 'express';
import dotenv from 'dotenv';
import {
  corsMiddleware,
  developmentCors,
  errorHandler,
  notFoundHandler,
  apiLimiter,
  createServiceLogger,
} from '@crm/shared';
import leadRoutes from './routes/leads';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.LEAD_SERVICE_PORT || 3002;
const logger = createServiceLogger('lead-service');

// Middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// CORS
if (process.env.NODE_ENV === 'development') {
  app.use(developmentCors);
} else {
  app.use(corsMiddleware);
}

// Rate limiting
app.use(apiLimiter);

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'lead-service',
    version: '1.0.0',
  });
});

// Routes
app.use('/', leadRoutes);

// Error handling
app.use(notFoundHandler);
app.use(errorHandler);

// Start server
app.listen(PORT, () => {
  logger.info(`👥 Lead Service running on port ${PORT}`);
});

export default app;
