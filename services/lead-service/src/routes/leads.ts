import { Router } from 'express';
import {
  validateBody,
  validateQuery,
  validateParams,
  createLeadSchema,
  updateLeadSchema,
  paginationSchema,
  idSchema,
  authenticateToken,
  requireAgent,
  asyncHandler,
} from '@crm/shared';
import * as leadController from '../controllers/leadController';

const router = Router();

// All routes require authentication
router.use(authenticateToken);
router.use(requireAgent);

// Lead CRUD operations
router.get('/', validateQuery(paginationSchema), asyncHandler(leadController.getLeads));
router.get('/:id', validateParams(idSchema), asyncHandler(leadController.getLeadById));
router.post('/', validateBody(createLeadSchema), asyncHandler(leadController.createLead));
router.put('/:id', validateParams(idSchema), validate<PERSON><PERSON>(updateLeadSchema), async<PERSON><PERSON><PERSON>(leadController.updateLead));
router.delete('/:id', validateParams(idSchema), asyncHandler(leadController.deleteLead));

// Lead assignment
router.put('/:id/assign', validateParams(idSchema), asyncHandler(leadController.assignLead));

// Lead status updates
router.put('/:id/status', validateParams(idSchema), asyncHandler(leadController.updateLeadStatus));

export default router;
