import express from 'express';
import helmet from 'helmet';
import compression from 'compression';
import morgan from 'morgan';
import dotenv from 'dotenv';
import { createProxyMiddleware } from 'http-proxy-middleware';
import {
  corsMiddleware,
  developmentC<PERSON>,
  errorH<PERSON>ler,
  notFound<PERSON><PERSON>ler,
  generalLimiter,
  createServiceLogger,
} from '@crm/shared';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.API_GATEWAY_PORT || 3000;
const logger = createServiceLogger('api-gateway');

// Security middleware
app.use(helmet());
app.use(compression());

// CORS
if (process.env.NODE_ENV === 'development') {
  app.use(developmentCors);
} else {
  app.use(corsMiddleware);
}

// Logging
app.use(morgan('combined'));

// Rate limiting
app.use(generalLimiter);

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'api-gateway',
    version: '1.0.0',
  });
});

// Service proxy configurations
const services = {
  auth: {
    target: `http://localhost:${process.env.AUTH_SERVICE_PORT || 3001}`,
    pathRewrite: { '^/api/auth': '' },
  },
  leads: {
    target: `http://localhost:${process.env.LEAD_SERVICE_PORT || 3002}`,
    pathRewrite: { '^/api/leads': '' },
  },
  contacts: {
    target: `http://localhost:${process.env.CONTACT_SERVICE_PORT || 3003}`,
    pathRewrite: { '^/api/contacts': '' },
  },
  properties: {
    target: `http://localhost:${process.env.PROPERTY_SERVICE_PORT || 3004}`,
    pathRewrite: { '^/api/properties': '' },
  },
  deals: {
    target: `http://localhost:${process.env.DEAL_SERVICE_PORT || 3005}`,
    pathRewrite: { '^/api/deals': '' },
  },
  notifications: {
    target: `http://localhost:${process.env.NOTIFICATION_SERVICE_PORT || 3006}`,
    pathRewrite: { '^/api/notifications': '' },
  },
};

// Create proxy middleware for each service
Object.entries(services).forEach(([serviceName, config]) => {
  app.use(
    `/api/${serviceName}`,
    createProxyMiddleware({
      target: config.target,
      changeOrigin: true,
      pathRewrite: config.pathRewrite,
      onError: (err, req, res) => {
        logger.error(`Proxy error for ${serviceName}:`, err);
        res.status(503).json({
          success: false,
          error: 'SERVICE_UNAVAILABLE',
          message: `${serviceName} service is currently unavailable`,
          timestamp: new Date().toISOString(),
          path: req.originalUrl,
        });
      },
      onProxyReq: (proxyReq, req, res) => {
        logger.info(`Proxying ${req.method} ${req.originalUrl} to ${serviceName}`);
      },
    })
  );
});

// API documentation endpoint
app.get('/api', (req, res) => {
  res.json({
    name: 'Real Estate CRM API',
    version: '1.0.0',
    description: 'API Gateway for Real Estate CRM microservices',
    services: {
      auth: '/api/auth - Authentication and authorization',
      leads: '/api/leads - Lead management',
      contacts: '/api/contacts - Contact management',
      properties: '/api/properties - Property listings',
      deals: '/api/deals - Deal tracking',
      notifications: '/api/notifications - Reminders and notifications',
    },
    endpoints: {
      health: '/health - Service health check',
      docs: '/api - This documentation',
    },
  });
});

// Error handling
app.use(notFoundHandler);
app.use(errorHandler);

// Start server
app.listen(PORT, () => {
  logger.info(`🚀 API Gateway running on port ${PORT}`);
  logger.info(`📚 API Documentation available at http://localhost:${PORT}/api`);
  logger.info(`🏥 Health check available at http://localhost:${PORT}/health`);
});

export default app;
