{"level":"info","message":"🚀 API Gateway running on port 3000","service":"crm-service","timestamp":"2025-05-30T21:45:13.987Z"}
{"level":"info","message":"📚 API Documentation available at http://localhost:3000/api","service":"crm-service","timestamp":"2025-05-30T21:45:13.988Z"}
{"level":"info","message":"🏥 Health check available at http://localhost:3000/health","service":"crm-service","timestamp":"2025-05-30T21:45:13.988Z"}
{"level":"info","message":"Proxying POST /api/auth/login to auth","service":"crm-service","timestamp":"2025-05-30T21:47:09.705Z"}
{"level":"info","message":"🚀 API Gateway running on port 3000","service":"crm-service","timestamp":"2025-05-30T21:48:10.748Z"}
{"level":"info","message":"📚 API Documentation available at http://localhost:3000/api","service":"crm-service","timestamp":"2025-05-30T21:48:10.749Z"}
{"level":"info","message":"🏥 Health check available at http://localhost:3000/health","service":"crm-service","timestamp":"2025-05-30T21:48:10.749Z"}
