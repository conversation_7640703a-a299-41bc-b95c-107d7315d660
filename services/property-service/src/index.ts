import express from 'express';
import dotenv from 'dotenv';
import {
  corsMiddleware,
  developmentCors,
  errorHandler,
  notFoundHandler,
  apiLimiter,
  createServiceLogger,
} from '@crm/shared';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PROPERTY_SERVICE_PORT || 3004;
const logger = createServiceLogger('property-service');

// Middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// CORS
if (process.env.NODE_ENV === 'development') {
  app.use(developmentCors);
} else {
  app.use(corsMiddleware);
}

// Rate limiting
app.use(apiLimiter);

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'property-service',
    version: '1.0.0',
  });
});

// TODO: Add property routes
app.get('/', (req, res) => {
  res.json({
    message: 'Property Service - Coming Soon',
    endpoints: {
      health: '/health',
      properties: '/ (CRUD operations)',
    },
  });
});

// Error handling
app.use(notFoundHandler);
app.use(errorHandler);

// Start server
app.listen(PORT, () => {
  logger.info(`🏠 Property Service running on port ${PORT}`);
});

export default app;
