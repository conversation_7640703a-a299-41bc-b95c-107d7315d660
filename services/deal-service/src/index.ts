import express from 'express';
import dotenv from 'dotenv';
import {
  corsMiddleware,
  developmentCors,
  errorHandler,
  notFoundHandler,
  apiLimiter,
  createServiceLogger,
} from '@crm/shared';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.DEAL_SERVICE_PORT || 3005;
const logger = createServiceLogger('deal-service');

// Middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// CORS
if (process.env.NODE_ENV === 'development') {
  app.use(developmentCors);
} else {
  app.use(corsMiddleware);
}

// Rate limiting
app.use(apiLimiter);

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'deal-service',
    version: '1.0.0',
  });
});

// TODO: Add deal routes
app.get('/', (req, res) => {
  res.json({
    message: 'Deal Service - Coming Soon',
    endpoints: {
      health: '/health',
      deals: '/ (CRUD operations)',
    },
  });
});

// Error handling
app.use(notFoundHandler);
app.use(errorHandler);

// Start server
app.listen(PORT, () => {
  logger.info(`💼 Deal Service running on port ${PORT}`);
});

export default app;
