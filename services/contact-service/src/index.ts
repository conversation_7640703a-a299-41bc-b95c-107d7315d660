import express from 'express';
import dotenv from 'dotenv';
import {
  corsMiddleware,
  developmentCors,
  errorHandler,
  notFoundHandler,
  apiLimiter,
  createServiceLogger,
} from '@crm/shared';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.CONTACT_SERVICE_PORT || 3003;
const logger = createServiceLogger('contact-service');

// Middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// CORS
if (process.env.NODE_ENV === 'development') {
  app.use(developmentCors);
} else {
  app.use(corsMiddleware);
}

// Rate limiting
app.use(apiLimiter);

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'contact-service',
    version: '1.0.0',
  });
});

// TODO: Add contact routes
app.get('/', (req, res) => {
  res.json({
    message: 'Contact Service - Coming Soon',
    endpoints: {
      health: '/health',
      contacts: '/ (CRUD operations)',
    },
  });
});

// Error handling
app.use(notFoundHandler);
app.use(errorHandler);

// Start server
app.listen(PORT, () => {
  logger.info(`📞 Contact Service running on port ${PORT}`);
});

export default app;
